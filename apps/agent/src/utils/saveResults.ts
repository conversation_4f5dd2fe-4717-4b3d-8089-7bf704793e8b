import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';
import { workflowDbService } from '@/services/index';
import * as XLSX from 'xlsx';

/**
 * Comprehensive creator scouting results structure for saving to file
 */
export interface SavedCreatorResults {
  metadata: {
    timestamp: string;
    inputParameters: any;
    summary: {
      totalScoutedCreators: number;
      totalQualifiedCreators: number;
      targetCreatorCount: number;
      filterMode: string;
      useIntelligentSelection: boolean;
    };
    filterSummary?: {
      mode: string;
      total_analyzed: number;
      total_qualified: number;
      avg_match_score: number;
      tier_breakdown: {
        PERFECT: number;
        EXCELLENT: number;
        GOOD: number;
        ACCEPTABLE: number;
      };
    };
  };

  // Detailed format - full creator data
  detailed: {
    creators: Array<{
      url: string;
      reason: string;
      match_score?: number;
      tier?: 'PERFECT' | 'EXCELLENT' | 'GOOD' | 'ACCEPTABLE';
      content_tags?: string[];
      creatorMetrics?: {
        unique_id: string;
        nickname: string;
        follower_count?: number;
        aweme_count?: number;
        create_time?: number;
        region?: string;
        language?: string;
        recentVideosCollected: number;
        medianViews: number;
        medianLikes: number;
        medianComments: number;
        medianShares: number;
        avgEngagementRate: number;
        totalViews: number;
        totalLikes: number;
        totalComments: number;
        totalShares: number;
        averageViews?: number;
        averageLikes?: number;
        averageComments?: number;
      };
    }>;
    totalCount: number;
  };

  // Summary format - essential information only
  summary: {
    totalQualified: number;
    targetCount: number;
    filterMode: string;
    topCreators: Array<{
      handle: string;
      url: string;
      score?: number;
      tier?: string;
      followers?: number;
      engagement?: number;
      reason: string;
    }>;
  };

  // CSV-ready format - flattened data for spreadsheet export
  csvReady: Array<{
    handle: string;
    nickname: string;
    url: string;
    match_score: number;
    tier: string;
    follower_count: number;
    post_count: number;
    median_views: number;
    median_likes: number;
    median_comments: number;
    median_shares: number;
    average_views: number;
    average_likes: number;
    average_comments: number;
    engagement_rate: number;
    region: string;
    language: string;
    content_tags: string;
    reason: string;
  }>;
}

/**
 * Convert array to CSV-friendly string
 * @param arr Array to convert
 * @returns Comma-separated string
 */
function arrayToCSVString(arr: any[] | undefined): string {
  if (!arr || arr.length === 0) return '';
  return arr.join('; ');
}

/**
 * Extract email address from TikTok signature.
 *
 * @param signature - The TikTok signature text
 * @returns First email found, or null if no email is found
 */
export function extractEmail(signature: string): string | null {
  if (!signature) {
    return null;
  }
  // Email regex pattern - matches most common email formats
  const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;

  const matches = signature.match(emailPattern);
  return matches ? matches[0] : null;
}

/**
 * Extract website URL from TikTok signature.
 *
 * @param signature - The TikTok signature text
 * @returns First website URL found, or null if no URL is found
 */
export function extractWebsite(signature: string): string | null {
  if (!signature) {
    return null;
  }
  // Website regex patterns - handles various URL formats commonly found in social media
  const patterns = [
    // Full URLs with protocol
    /https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)/gi,
    // URLs without protocol but with www
    /www\.[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)/gi,
    // Domain-only patterns (common in TikTok bios)
    /\b[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(?:\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})*\.(?:com|org|net|edu|gov|mil|int|co|io|me|tv|app|dev|tech|shop|store|blog|info|biz|name|pro|mobi|travel|museum|aero|jobs|cat|tel|xxx|asia|coop|post|geo|xxx|local)\b/gi,
  ];

  for (const pattern of patterns) {
    const matches = signature.match(pattern);
    if (matches) {
      let url = matches[0];

      // Add protocol if missing
      if (!url.startsWith('http')) {
        url = 'https://' + url;
      }

      return url;
    }
  }

  return null;
}

/**
 * Convert creators data to CSV format
 * @param creators Array of creator objects
 * @returns CSV string
 */
function convertToCSV(creators: any[]): string {
  if (!creators || creators.length === 0) return '';

  // Define CSV headers
  const headers = [
    'Handle',
    'Nickname',
    'URL',
    'Match Score',
    'Email',
    'Website',
    'YoutubeId',
    'InstagramId',
    'TwitterId',
    'Follower Count',
    'Post Count',
    'Median Views',
    'Median Likes',
    'Median Comments',
    'Engagement Rate (%)',
    'Region',
    'Language',
    'Content Tags',
    'Reason',
  ];

  // Create CSV rows
  const rows = creators.map((creator) => {
    const metrics = creator.creatorMetrics || {};
    return [
      metrics.unique_id || '',
      metrics.nickname || '',
      creator.url || '',
      creator.match_score?.toFixed(2) || '0',
      extractEmail(metrics.signature) || '',
      extractWebsite(metrics.signature) || '',
      metrics.youtube_channel_id || '',
      metrics.ins_id || '',
      metrics.twitter_id || '',
      metrics.follower_count || 0,
      metrics.aweme_count || 0,
      metrics.medianViews || 0,
      metrics.medianLikes || 0,
      metrics.medianComments || 0,
      metrics.avgEngagementRate || 0,
      metrics.region || '',
      metrics.language || '',
      arrayToCSVString(creator.content_tags),
      `"${(creator.reason || '').replace(/"/g, '""')}"`, // Escape quotes in reason
    ];
  });

  // Combine headers and rows
  const csvContent = [
    headers.join(','),
    ...rows.map((row) => row.join(',')),
  ].join('\n');

  return csvContent;
}

/**
 * Save creator scouting results to a comprehensive JSON file with multiple formats
 * @param results The workflow results to save
 * @param inputData The original input parameters
 * @param customDir Optional custom directory to save results (default: 'scouting-results')
 * @param customFilename Optional custom filename prefix (default: 'creator-scout-results')
 * @returns The full path to the saved file
 */
export async function saveCreatorResultsToFile(
  executeResult: any,
  inputData: any,
  customDir?: string,
  customFilename?: string,
): Promise<string> {
  try {
    // Create results directory if it doesn't exist
    const resultsDir = join(process.cwd(), customDir || 'scouting-results');
    mkdirSync(resultsDir, { recursive: true });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filenamePrefix = customFilename || 'creator-scout-results';
    const jsonFilename = `${filenamePrefix}-${timestamp}.json`;
    const csvFilename = `${filenamePrefix}-${timestamp}.csv`;
    const jsonFilepath = join(resultsDir, jsonFilename);
    const csvFilepath = join(resultsDir, csvFilename);

    const results = executeResult.result;

    // Check if we have a contextId to retrieve full results from database
    let creatorResults = [];
    let fullContextData = null;

    if (results.contextId) {
      // Retrieve full results from database using contextId
      console.log(
        `🔍 Retrieving full results from database using contextId: ${results.contextId}`,
      );

      fullContextData = await getCreatorScoutResultsByContextId(
        results.contextId,
      );

      if (fullContextData && fullContextData.contextData.results) {
        creatorResults = fullContextData.contextData.results;
        console.log(
          `✅ Retrieved ${creatorResults.length} creators from database`,
        );
      } else {
        console.warn('⚠️  No results found in database context');
      }
    } else {
      // Fallback to old structure if no contextId (backward compatibility)
      creatorResults = results.results || [];
    }

    // Calculate average match score
    const matchScores = creatorResults
      .map((c: any) => c.match_score)
      .filter((score: any) => score !== undefined);
    const avgMatchScore =
      matchScores.length > 0
        ? matchScores.reduce((sum: number, score: number) => sum + score, 0) /
          matchScores.length
        : 0;

    // Prepare comprehensive results data with multiple formats
    const comprehensiveResults: SavedCreatorResults = {
      metadata: {
        timestamp: new Date().toISOString(),
        inputParameters: inputData,
        summary: {
          totalScoutedCreators: results.scoutedCreators || 0,
          totalQualifiedCreators: results.qualifiedCreators || 0,
          targetCreatorCount: inputData.desiredCreatorCount || 0,
          filterMode: inputData.filterMode || 'UNKNOWN',
          useIntelligentSelection:
            inputData.useIntelligentChallengeSelection || false,
        },
        filterSummary: results.filterSummary
          ? {
              ...results.filterSummary,
              avg_match_score: avgMatchScore,
            }
          : undefined,
      },

      // Detailed format - full creator data
      detailed: {
        creators: creatorResults,
        totalCount: creatorResults.length,
      },

      // Summary format - top creators
      summary: {
        totalQualified: creatorResults.length,
        targetCount: inputData.desiredCreatorCount || 0,
        filterMode: inputData.filterMode || 'UNKNOWN',
        topCreators: creatorResults.slice(0, 10).map((creator: any) => ({
          handle: creator.creatorMetrics?.unique_id || '',
          url: creator.url || '',
          score: creator.match_score,
          tier: creator.tier,
          followers: creator.creatorMetrics?.follower_count,
          engagement: creator.creatorMetrics?.avgEngagementRate,
          reason: creator.reason || '',
        })),
      },

      // CSV-ready format
      csvReady: creatorResults.map((creator: any) => ({
        handle: creator.creatorMetrics?.unique_id || '',
        nickname: creator.creatorMetrics?.nickname || '',
        url: creator.url || '',
        match_score: creator.match_score || 0,
        tier: creator.tier || '',
        follower_count: creator.creatorMetrics?.follower_count || 0,
        post_count: creator.creatorMetrics?.aweme_count || 0,
        median_views: creator.creatorMetrics?.medianViews || 0,
        median_likes: creator.creatorMetrics?.medianLikes || 0,
        median_comments: creator.creatorMetrics?.medianComments || 0,
        median_shares: creator.creatorMetrics?.medianShares || 0,
        average_views: creator.creatorMetrics?.averageViews || 0,
        average_likes: creator.creatorMetrics?.averageLikes || 0,
        average_comments: creator.creatorMetrics?.averageComments || 0,
        engagement_rate: creator.creatorMetrics?.avgEngagementRate || 0,
        signature: creator.creatorMetrics?.signature || '',
        youtube_channel_id: creator.creatorMetrics?.youtube_channel_id || '',
        ins_id: creator.creatorMetrics?.ins_id || '',
        twitter_id: creator.creatorMetrics?.twitter_id || '',
        region: creator.creatorMetrics?.region || '',
        language: creator.creatorMetrics?.language || '',
        content_tags: arrayToCSVString(creator.content_tags),
        reason: creator.reason || '',
      })),
    };

    // Save JSON file with pretty formatting
    writeFileSync(
      jsonFilepath,
      JSON.stringify(comprehensiveResults, null, 2),
      'utf8',
    );

    // Save CSV file
    const csvContent = convertToCSV(creatorResults);
    writeFileSync(csvFilepath, csvContent, 'utf8');

    // Log success information
    console.log(`\n💾 COMPREHENSIVE RESULTS SAVED:`);
    console.log(`📁 JSON Location: ${jsonFilepath}`);
    console.log(`📁 CSV Location: ${csvFilepath}`);
    console.log(`📊 Total Creators: ${creatorResults.length}`);
    console.log(
      `📋 Formats saved: JSON (detailed, summary, CSV-ready) + CSV file`,
    );

    if (results.filterSummary?.tier_breakdown) {
      console.log(
        `🏆 PERFECT Tier: ${results.filterSummary.tier_breakdown.PERFECT || 0}`,
      );
      console.log(
        `⭐ EXCELLENT Tier: ${results.filterSummary.tier_breakdown.EXCELLENT || 0}`,
      );
      console.log(
        `👍 GOOD Tier: ${results.filterSummary.tier_breakdown.GOOD || 0}`,
      );
      console.log(
        `✅ ACCEPTABLE Tier: ${results.filterSummary.tier_breakdown.ACCEPTABLE || 0}`,
      );
    }

    return jsonFilepath;
  } catch (error) {
    console.error('❌ Error saving results to file:', error);
    throw error;
  }
}

/**
 * Save results with a custom format for analysis or reporting
 * @param results The workflow results
 * @param inputData The input parameters
 * @param format The output format ('detailed' | 'summary' | 'csv-ready')
 * @returns The path to the saved file
 */
export async function saveCreatorResultsWithFormat(
  results: any,
  inputData: any,
  _format: 'detailed' | 'summary' | 'csv-ready' = 'detailed',
): Promise<string> {
  console.log(
    `⚠️  saveCreatorResultsWithFormat is deprecated. All formats are now included in one comprehensive file.`,
  );
  return saveCreatorResultsToFile(results, inputData);
}

/**
 * Check if results directory exists and create it if needed
 * @param customDir Optional custom directory name
 */
export function ensureResultsDirectory(customDir?: string): string {
  const resultsDir = join(process.cwd(), customDir || 'scouting-results');

  if (!existsSync(resultsDir)) {
    mkdirSync(resultsDir, { recursive: true });
    console.log(`📁 Created results directory: ${resultsDir}`);
  }

  return resultsDir;
}

/**
 * Helper function to retrieve only the final filtered results
 * @param contextId The context ID from the workflow output
 * @returns Final creator scout results
 */
export async function getCreatorScoutResultsByContextId(contextId: number) {
  return await workflowDbService.getContextById(contextId);
}

/**
 * Convert creators data to Excel format with multiple worksheets
 * @param creators Array of creator objects
 * @param campaignMetadata Campaign metadata for summary sheet
 * @returns Excel workbook buffer
 */
function convertToExcel(creators: any[], campaignMetadata?: any): Buffer {
  const workbook = XLSX.utils.book_new();

  // 1. Summary Sheet
  const summaryData = [
    ['Campaign Summary'],
    [''],
    ['Campaign Name', campaignMetadata?.campaignName || 'N/A'],
    ['Campaign ID', campaignMetadata?.campaignId || 'N/A'],
    ['Description', campaignMetadata?.description || 'N/A'],
    ['Total KOLs Found', creators.length],
    ['Target KOL Count', campaignMetadata?.targetKOLCount || 'N/A'],
    [
      'Workflow Runs Completed',
      campaignMetadata?.workflowRunsCompleted || 'N/A',
    ],
    [
      'Total Execution Time',
      campaignMetadata?.totalExecutionTime
        ? formatDuration(campaignMetadata.totalExecutionTime)
        : 'N/A',
    ],
    [
      'Average KOLs per Run',
      campaignMetadata?.averageKOLsPerRun?.toFixed(1) || 'N/A',
    ],
    ['Successful Runs', campaignMetadata?.successfulRuns || 'N/A'],
    ['Failed Runs', campaignMetadata?.failedRuns || 'N/A'],
    [''],
    ['Tier Breakdown'],
    ['PERFECT', creators.filter((c) => c.tier === 'PERFECT').length],
    ['EXCELLENT', creators.filter((c) => c.tier === 'EXCELLENT').length],
    ['GOOD', creators.filter((c) => c.tier === 'GOOD').length],
    ['ACCEPTABLE', creators.filter((c) => c.tier === 'ACCEPTABLE').length],
    [''],
    ['Generated On', new Date().toISOString()],
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Campaign Summary');

  // 2. Detailed Results Sheet
  const detailedHeaders = [
    'Handle',
    'Nickname',
    'URL',
    'Match Score',
    'Tier',
    'Email',
    'Website',
    'YouTube ID',
    'Instagram ID',
    'Twitter ID',
    'Follower Count',
    'Post Count',
    'Median Views',
    'Median Likes',
    'Median Comments',
    'Median Shares',
    'Average Views',
    'Average Likes',
    'Average Comments',
    'Engagement Rate (%)',
    'Region',
    'Language',
    'Content Tags',
    'Reason',
  ];

  const detailedRows = creators.map((creator) => {
    const metrics = creator.creatorMetrics || {};
    return [
      metrics.unique_id || '',
      metrics.nickname || '',
      creator.url || '',
      creator.match_score?.toFixed(2) || '0',
      creator.tier || '',
      extractEmail(metrics.signature) || '',
      extractWebsite(metrics.signature) || '',
      metrics.youtube_channel_id || '',
      metrics.ins_id || '',
      metrics.twitter_id || '',
      metrics.follower_count || 0,
      metrics.aweme_count || 0,
      metrics.medianViews || 0,
      metrics.medianLikes || 0,
      metrics.medianComments || 0,
      metrics.medianShares || 0,
      metrics.averageViews || 0,
      metrics.averageLikes || 0,
      metrics.averageComments || 0,
      metrics.avgEngagementRate || 0,
      metrics.region || '',
      metrics.language || '',
      arrayToCSVString(creator.content_tags),
      creator.reason || '',
    ];
  });

  const detailedData = [detailedHeaders, ...detailedRows];
  const detailedSheet = XLSX.utils.aoa_to_sheet(detailedData);

  // Auto-size columns for better readability
  const detailedColWidths = detailedHeaders.map((_, index) => {
    const maxLength = Math.max(
      detailedHeaders[index].length,
      ...detailedRows.map((row) => String(row[index] || '').length),
    );
    return { wch: Math.min(Math.max(maxLength + 2, 10), 50) };
  });
  detailedSheet['!cols'] = detailedColWidths;

  XLSX.utils.book_append_sheet(workbook, detailedSheet, 'Detailed Results');

  // 3. Top Performers Sheet (sorted by match score)
  const topPerformers = creators
    .filter((c) => c.match_score !== undefined)
    .sort((a, b) => (b.match_score || 0) - (a.match_score || 0))
    .slice(0, 50); // Top 50

  const topPerformersHeaders = [
    'Rank',
    'Handle',
    'Nickname',
    'Match Score',
    'Tier',
    'Follower Count',
    'Engagement Rate (%)',
    'Median Views',
    'Region',
    'URL',
  ];

  const topPerformersRows = topPerformers.map((creator, index) => {
    const metrics = creator.creatorMetrics || {};
    return [
      index + 1,
      metrics.unique_id || '',
      metrics.nickname || '',
      creator.match_score?.toFixed(2) || '0',
      creator.tier || '',
      metrics.follower_count || 0,
      metrics.avgEngagementRate || 0,
      metrics.medianViews || 0,
      metrics.region || '',
      creator.url || '',
    ];
  });

  const topPerformersData = [topPerformersHeaders, ...topPerformersRows];
  const topPerformersSheet = XLSX.utils.aoa_to_sheet(topPerformersData);
  XLSX.utils.book_append_sheet(workbook, topPerformersSheet, 'Top Performers');

  // Convert workbook to buffer
  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
}

/**
 * Helper function to format duration for Excel display
 */
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Save completed campaign scouted KOLs to an Excel file
 * @param campaignState The campaign state containing all results
 * @param campaignConfig The campaign configuration
 * @param customDir Optional custom directory to save results (default: campaign output directory)
 * @param customFilename Optional custom filename prefix (default: 'campaign-final-results')
 * @returns The full path to the saved Excel file
 */
export async function saveCampaignResultsToExcel(
  campaignState: any,
  campaignConfig: any,
  customDir?: string,
  customFilename?: string,
): Promise<string> {
  try {
    // Create results directory if it doesn't exist
    const resultsDir =
      customDir ||
      campaignConfig.outputDirectory ||
      join(process.cwd(), 'campaign-results');
    mkdirSync(resultsDir, { recursive: true });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filenamePrefix = customFilename || 'campaign-final-results';
    const excelFilename = `${filenamePrefix}-${timestamp}.xlsx`;
    const excelFilepath = join(resultsDir, excelFilename);

    // Prepare campaign metadata for Excel summary
    const campaignMetadata = {
      campaignName: campaignConfig.campaignName,
      campaignId: campaignConfig.campaignId,
      description: campaignConfig.description,
      targetKOLCount: campaignConfig.targetKOLCount,
      workflowRunsCompleted: campaignState.workflowRunsCompleted,
      totalExecutionTime: campaignState.statistics?.totalExecutionTime,
      averageKOLsPerRun: campaignState.statistics?.averageKOLsPerRun,
      successfulRuns: campaignState.statistics?.successfulRuns,
      failedRuns: campaignState.statistics?.failedRuns,
    };

    // Get all results from campaign state
    const allResults = campaignState.allResults || [];

    console.log(`\n📊 PREPARING EXCEL EXPORT:`);
    console.log(`📋 Campaign: ${campaignConfig.campaignName}`);
    console.log(`📊 Total KOLs: ${allResults.length}`);
    console.log(`🔄 Workflow Runs: ${campaignState.workflowRunsCompleted}`);

    // Generate Excel workbook
    const excelBuffer = convertToExcel(allResults, campaignMetadata);

    // Save Excel file
    writeFileSync(excelFilepath, excelBuffer);

    // Log success information
    console.log(`\n💾 CAMPAIGN EXCEL RESULTS SAVED:`);
    console.log(`📁 Excel Location: ${excelFilepath}`);
    console.log(`📊 Total KOLs: ${allResults.length}`);
    console.log(
      `📋 Worksheets: Campaign Summary, Detailed Results, Top Performers`,
    );

    // Generate tier breakdown for console output
    const tierBreakdown = {
      PERFECT: allResults.filter((c: any) => c.tier === 'PERFECT').length,
      EXCELLENT: allResults.filter((c: any) => c.tier === 'EXCELLENT').length,
      GOOD: allResults.filter((c: any) => c.tier === 'GOOD').length,
      ACCEPTABLE: allResults.filter((c: any) => c.tier === 'ACCEPTABLE').length,
    };

    console.log(`🏆 PERFECT Tier: ${tierBreakdown.PERFECT}`);
    console.log(`⭐ EXCELLENT Tier: ${tierBreakdown.EXCELLENT}`);
    console.log(`👍 GOOD Tier: ${tierBreakdown.GOOD}`);
    console.log(`✅ ACCEPTABLE Tier: ${tierBreakdown.ACCEPTABLE}`);

    return excelFilepath;
  } catch (error) {
    console.error('❌ Error saving campaign results to Excel:', error);
    throw error;
  }
}

/**
 * Save batch results to Excel (for individual workflow runs)
 * @param batchResult The batch result from a single workflow run
 * @param campaignConfig The campaign configuration
 * @param batchNumber The batch number
 * @param customDir Optional custom directory to save results
 * @returns The full path to the saved Excel file
 */
export async function saveBatchResultsToExcel(
  batchResult: any,
  campaignConfig: any,
  batchNumber: number,
  customDir?: string,
): Promise<string> {
  try {
    // Create results directory if it doesn't exist
    const resultsDir =
      customDir ||
      campaignConfig.outputDirectory ||
      join(process.cwd(), 'campaign-results');
    mkdirSync(resultsDir, { recursive: true });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const excelFilename = `campaign-${campaignConfig.campaignId}-batch-${batchNumber}-${timestamp}.xlsx`;
    const excelFilepath = join(resultsDir, excelFilename);

    // Prepare batch metadata for Excel summary
    const batchMetadata = {
      campaignName: campaignConfig.campaignName,
      campaignId: campaignConfig.campaignId,
      batchNumber: batchNumber,
      newUniqueKOLs: batchResult.newUniqueKOLs,
      totalUniqueKOLs: batchResult.totalUniqueKOLs,
      executionTime: batchResult.executionTime,
      timestamp: batchResult.timestamp,
    };

    // Generate Excel workbook for batch results
    const excelBuffer = convertToExcel(
      batchResult.results || [],
      batchMetadata,
    );

    // Save Excel file
    writeFileSync(excelFilepath, excelBuffer);

    console.log(
      `💾 Batch ${batchNumber} Excel results saved to ${excelFilepath}`,
    );

    return excelFilepath;
  } catch (error) {
    console.error(
      `❌ Error saving batch ${batchNumber} results to Excel:`,
      error,
    );
    throw error;
  }
}
