import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import { <PERSON><PERSON> } from '@mastra/core';
import {
  creatorScoutWorkflow,
  creatorDirectScoutWorkflow,
} from '@/workflows/creatorScoutWorkflow';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import { select } from '@inquirer/prompts';
import { videoScouter } from '@/services/scouting/videoScouter';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { WorkflowNames } from '@repo/constants';

/**
 * Load existing KOLs from file
 */
function loadExistingKols(): string[] {
  const kolsFilePath = join(process.cwd(), 'src/tests/hsr_kols.txt');

  if (!existsSync(kolsFilePath)) {
    console.warn(
      `⚠️ existing_kols.txt not found at ${kolsFilePath}, proceeding without existing KOL exclusions`,
    );
    return [];
  }

  try {
    const fileContent = readFileSync(kolsFilePath, 'utf8');
    const kolIds = fileContent
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0); // Filter out empty lines

    console.log(
      `📋 Loaded ${kolIds.length} existing KOL IDs from ${kolsFilePath}`,
    );
    console.log(`🚫 These KOLs will be excluded from new scouting results`);
    return kolIds;
  } catch (error) {
    console.error(
      `❌ Failed to load existing KOLs from ${kolsFilePath}:`,
      error,
    );
    return [];
  }
}

// Register the workflow and agents
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: {
    [WorkflowNames.creatorScoutWorkflow]: creatorScoutWorkflow,
    [WorkflowNames.creatorDirectScoutWorkflow]: creatorDirectScoutWorkflow,
  },
});

const conquerorBladeCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'conquerors-blade',
  campaignName: 'Conquerors Blade Campaign',
  description:
    'Find English speaking KOLs from US or UK who post about Conquerors Blade',

  // Campaign-level targets
  targetKOLCount: 300,
  kolPerTask: 80,
  maxWorkflowRuns: 10,

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Conqueror's Blade gameplay, tutorials, and guides
        - Conqueror's Blade unit reviews and tier lists
        - Conqueror's Blade battle tactics and strategy content
        - Conqueror's Blade season updates and patch reviews
        - Conqueror's Blade house/clan content and events
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Primary focus on Conqueror's Blade content
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Existing KOL filtering - exclude these unique_ids from new scouting
    excludeExistingKolIds: [],
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run

  // Export settings
  enableExcelExport: true,
  enableBatchExcelExport: false,
};

const mountNBladeCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'mount-n-blade',
  campaignName: 'Mount & Blade Campaign',
  description:
    'Find English speaking KOLs from US or Europe who create Mount & Blade content and related medieval gaming content',

  // Campaign-level targets
  targetKOLCount: 300,
  kolPerTask: 80,
  maxWorkflowRuns: 10,

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Mount & Blade series content (Mount & Blade, Warband, Bannerlord)
        - Mount & Blade gameplay, tutorials, reviews, and guides
        - Mount & Blade mods and modding content
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Primary focus on Mount & Blade series
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Existing KOL filtering - exclude these unique_ids from new scouting
    excludeExistingKolIds: [],
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run

  // Export settings
  enableExcelExport: true,
  enableBatchExcelExport: false,
};

const coldWeaponsCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'cold-weapons-war-culture',
  campaignName: 'Cold Weapons & War Culture Campaign',
  description:
    'Find English speaking KOLs who post about cold weapons, ancient warfare, and war culture',

  // Campaign-level targets
  targetKOLCount: 300,
  kolPerTask: 80,
  maxWorkflowRuns: 10,

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Cold weapons enthusiasts (swords, knives, medieval weapons, traditional weapons)
        - Ancient warfare and military history
        - War culture and military traditions
        - Historical battles and tactics
        - Weapon crafting and blacksmithing
        - HEMA (Historical European Martial Arts) practitioners
        - Traditional martial arts with weapons focus
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Content should focus on cold weapons, ancient warfare, or war culture
        - Educational or enthusiast content preferred
        - Historical accuracy and craftsmanship appreciation
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Existing KOL filtering - exclude these unique_ids from new scouting
    excludeExistingKolIds: [],
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run

  // Export settings
  enableExcelExport: true,
  enableBatchExcelExport: false,
};

const conquerorsBladeSeason22CampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'conquerors-blade-season-22',
  campaignName: 'Conquerors Blade Season 22 Campaign',
  description:
    'Find English speaking KOLs who post about cold weapons, ancient warfare, and war culture',

  // Campaign-level targets
  targetKOLCount: 300,
  kolPerTask: 80,
  maxWorkflowRuns: 10,

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Great Qin Dynasty culture and history
        - Terracotta Warriors and archaeological discoveries
        - Ancient Chinese war chariots and military units
        - Sun Tzu's Art of War references and strategies
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Content should focus on ancient Chinese history, warfare, and military culture
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Existing KOL filtering - exclude these unique_ids from new scouting
    excludeExistingKolIds: [],
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run

  // Export settings
  enableExcelExport: true,
  enableBatchExcelExport: false,
};

const indonesiaGamingCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'indonesia-hsr-gaming',
  campaignName: 'Indonesia Honkai Star Rail KOL Campaign',
  description:
    'Find Indonesian gaming influencers who actively create content for Honkai Star Rail and similar gacha/anime RPG games',

  // Campaign-level targets
  targetKOLCount: 500,
  kolPerTask: 60,
  maxWorkflowRuns: 20,

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find Indonesian gaming KOLs who can effectively promote Honkai Star Rail with the following criteria:

MANDATORY REQUIREMENTS:
- Region: Indonesian creators only
  - Must use Bahasa Indonesia in majority of content (titles, descriptions, commentary)
  - Channel/profile bio should indicate Indonesia location
  - Content clearly targeted at Indonesian gaming audience
  - Check for Indonesian cultural references, local gaming terminology

- Primary Gaming Focus - MUST create content for at least ONE:
  - Honkai Star Rail (崩坏：星穹铁道 / HSR)
  - Genshin Impact (原神)
  - Other turn-based gacha RPGs (Epic Seven, Arknights, Blue Archive, etc.)
  - Anime-style RPG games with similar aesthetics

- Content Quality Requirements:
  - Regular gaming content (at least 2-3 videos per week)
  - Must show face in videos (facecam during gameplay or IRL segments)
  - Professional video quality (clear audio, good editing)
  - Engaging personality that connects with audience

- Performance Metrics:
  - Follower Count: 100,000 - 5,000,000 (sweet spot for engagement)
  - Average View Count: Minimum 100,000 views per video
  - Engagement Rate: Above 5% (likes + comments / views)
  - Recent Activity: Posted within last 7 days

PREFERRED CONTENT TYPES:
- Character build guides and team compositions
- Gacha pull videos and reactions
- Story/event walkthroughs with commentary
- Game updates and patch reviews
- F2P guides and resource management tips
- Character tier lists and meta discussions
- Collaboration content with other gaming creators

AUDIENCE PROFILE CHECK:
- Comments section primarily in Indonesian
- Active community engagement (creator responds to comments)
- Audience demographic appears to be 16-35 years old
- Evidence of spending on gacha games in content

BONUS CRITERIA:
- Has promoted other gacha/mobile games before
- Shows spending behavior (dolphin/whale status)
- Creates content during game launches/major updates
- Has Discord/community server for fans
- Cross-platform presence (YouTube + TikTok/Instagram)

EXCLUSION CRITERIA:
- Pure gameplay channels without commentary
- Channels that only do news aggregation
- Creators who have publicly criticized gacha monetization
- Inactive creators (no posts in 30+ days)
- Channels with majority non-gaming content

FILTERING PRIORITY:
1. Active Honkai Star Rail content creation
2. Indonesian language and cultural relevance
3. Performance metrics (100K+ followers with high engagement)
4. Face reveal and personality-driven content
5. Evidence of gacha game enthusiasm/spending`,

    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'OPPORTUNITY' as const,
    searchMode: 'DIRECT_VIDEO' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 100000,
    minRecentAverageViews: 100000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Existing KOL filtering - exclude these unique_ids from new scouting
    excludeExistingKolIds: loadExistingKols(),
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 1, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run

  // Export settings
  enableExcelExport: true,
  enableBatchExcelExport: false,
};

/**
 * Demo function for Scout Campaign feature
 */
async function runScoutCampaignDemo(campaignConfig: CampaignConfig) {
  console.log('🚀 Starting Scout Campaign Demo...\n');

  try {
    // Create and run the campaign
    const campaign = new ScoutCampaign(campaignConfig, mastra);

    console.log('📋 Campaign Configuration:');
    console.log(`   🎯 Target KOLs: ${campaignConfig.targetKOLCount}`);
    console.log(`   📊 KOLs per task: ${campaignConfig.kolPerTask}`);
    console.log(`   🔄 Max workflow runs: ${campaignConfig.maxWorkflowRuns}`);
    console.log(`   💾 Output directory: ${campaignConfig.outputDirectory}`);
    console.log(`   🔧 Filter mode: ${campaignConfig.sharedConfig.filterMode}`);
    console.log(`   🏁 Picker mode: ${campaignConfig.sharedConfig.pickerMode}`);
    if (
      campaignConfig.sharedConfig.minViews &&
      campaignConfig.sharedConfig.minViews > 0
    ) {
      console.log(`   👀 Min views: ${campaignConfig.sharedConfig.minViews}`);
    }
    if (
      campaignConfig.sharedConfig.minLikes &&
      campaignConfig.sharedConfig.minLikes > 0
    ) {
      console.log(`   💖 Min likes: ${campaignConfig.sharedConfig.minLikes}`);
    }
    if (
      campaignConfig.sharedConfig.minComments &&
      campaignConfig.sharedConfig.minComments > 0
    ) {
      console.log(
        `   💬 Min comments: ${campaignConfig.sharedConfig.minComments}`,
      );
    }
    if (
      campaignConfig.sharedConfig.minFollowers &&
      campaignConfig.sharedConfig.minFollowers > 0
    ) {
      console.log(
        `   👥 Min followers: ${campaignConfig.sharedConfig.minFollowers}`,
      );
    }
    if (
      campaignConfig.sharedConfig.minRecentMedianViews &&
      campaignConfig.sharedConfig.minRecentMedianViews > 0
    ) {
      console.log(
        `   📈 Min median views: ${campaignConfig.sharedConfig.minRecentMedianViews}`,
      );
    }
    if (
      campaignConfig.sharedConfig.minRecentMedianLikes &&
      campaignConfig.sharedConfig.minRecentMedianLikes > 0
    ) {
      console.log(
        `   📈 Min median likes: ${campaignConfig.sharedConfig.minRecentMedianLikes}`,
      );
    }
    if (
      campaignConfig.sharedConfig.minRecentMedianComments &&
      campaignConfig.sharedConfig.minRecentMedianComments > 0
    ) {
      console.log(
        `   📈 Min median comments: ${campaignConfig.sharedConfig.minRecentMedianComments}`,
      );
    }
    console.log('');

    // Run the campaign
    const campaignResults = await campaign.runCampaign();

    console.log('\n🎉 Campaign completed successfully!');
    console.log(`📊 Total batches processed: ${campaignResults.length}`);

    // Display summary of each batch
    campaignResults.forEach((batch) => {
      console.log(`\n📋 Batch ${batch.batchNumber} Summary:`);
      console.log(`   🆕 New unique KOLs: ${batch.newUniqueKOLs}`);
      console.log(`   📊 Total unique KOLs: ${batch.totalUniqueKOLs}`);
      console.log(
        `   ⏱️ Execution time: ${formatDuration(batch.executionTime)}`,
      );
      console.log(`   📅 Timestamp: ${batch.timestamp}`);
    });

    // Display final campaign status
    const finalStatus = campaign.getCampaignStatus();
    console.log('\n📈 Final Campaign Statistics:');
    console.log(
      `   🎯 Total unique KOLs found: ${finalStatus.totalUniqueKOLs}`,
    );
    console.log(
      `   📊 Total scouted results: ${finalStatus.totalScoutedResults}`,
    );
    console.log(
      `   🔄 Workflow runs completed: ${finalStatus.workflowRunsCompleted}`,
    );
    console.log(
      `   ✅ Successful runs: ${finalStatus.statistics.successfulRuns}`,
    );
    console.log(`   ❌ Failed runs: ${finalStatus.statistics.failedRuns}`);
    console.log(
      `   📈 Average KOLs per run: ${finalStatus.statistics.averageKOLsPerRun.toFixed(1)}`,
    );
    console.log(
      `   ⏱️ Total execution time: ${formatDuration(finalStatus.statistics.totalExecutionTime)}`,
    );
  } catch (error) {
    console.error('❌ Campaign failed:', error);
    process.exit(1);
  }
}

/**
 * Utility function to format duration
 */
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Main function to run different demo scenarios
 */
async function main() {
  videoScouter.setPreferredService('tokapi');

  const demoType = await select({
    message: 'Select demo type:',
    choices: [
      { value: 'conquerorsBlade', name: 'Conquerors Blade Campaign' },
      { value: 'mountNBlade', name: 'Mount & Blade Campaign' },
      { value: 'coldWeapons', name: 'Cold Weapons & War Culture Campaign' },
      {
        value: 'conquerorsBladeSeason22',
        name: 'Conquerors Blade Season 22 Campaign',
      },
      { value: 'indonesiaGaming', name: 'Indonesia Gaming Campaign' },
    ],
  });

  console.log('🎯 Scout Campaign Demo System');
  console.log('============================\n');

  switch (demoType) {
    case 'conquerorsBlade':
      console.log('Running Conquerors Blade campaign...');
      await runScoutCampaignDemo(conquerorBladeCampaignConfig);
      break;

    case 'mountNBlade':
      console.log('Running Mount & Blade campaign...');
      await runScoutCampaignDemo(mountNBladeCampaignConfig);
      break;

    case 'coldWeapons':
      console.log('Running Cold Weapons & War Culture campaign...');
      await runScoutCampaignDemo(coldWeaponsCampaignConfig);
      break;

    case 'conquerorsBladeSeason22':
      console.log('Running Conquerors Blade Season 22 campaign...');
      await runScoutCampaignDemo(conquerorsBladeSeason22CampaignConfig);
      break;

    case 'indonesiaGaming':
      console.log('Running Indonesia Gaming campaign...');
      await runScoutCampaignDemo(indonesiaGamingCampaignConfig);
      break;

    default:
      break;
  }
}

// Run the demo if this file is executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export { runScoutCampaignDemo, formatDuration };
