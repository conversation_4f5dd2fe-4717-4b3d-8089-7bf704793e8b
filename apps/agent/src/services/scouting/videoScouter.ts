import { TiktokServiceSchema } from '@/schemas/tools_schema';
import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';
import { TikHubService } from './tikhub';
import { TokAPIService } from './tokapi';
import { scoutDbService, workflowDbService, s3Service } from '../index';
import { ChallengPickerOutputSchema } from '@/agents/challengePickerAgent';
import { Agent, ToolsInput } from '@mastra/core/agent';
import { Metric } from '@mastra/core';
import { FileUploadData, UploadResult } from '../storage/s3Service';

/**
 * Service configuration for TikTok API providers
 */
interface ServiceConfig {
  name: string;
  weight: number; // Higher weight = more likely to be selected
  cooldownMs: number; // Cooldown period after rate limit hit
  lastFailureTime?: number;
  consecutiveFailures: number;
  maxConsecutiveFailures: number;
}

/**
 * Configuration constants for video scouting operations
 * Export for external access and modification
 */
export const SCOUTING_CONFIG = {
  // Challenge selection parameters
  DEFAULT_TOP_N_CHALLENGES: 12,
  DEFAULT_SELECT_K_CHALLENGES: 6,
  CHALLENGE_PICKER_BATCH_SIZE: 50,

  // Video fetching parameters
  VIDEO_DIRECT_SEARCH_PAGESIZE: 25,
  MIN_VIDEO_PER_KEYWORD: 200,
  MAX_VIDEO_PER_CHALLENGE: 50,
  MIN_CREATOR_PER_CHALLENGE: 50,
  VIDEOS_PER_API_PAGE: 25,
  CREATOR_POSTS_PER_FETCH: 10,
  MAX_DIRECT_SEARCH_CURSOR_START: 30,
  MAX_DIRECT_SEARCH_CURSOR_LIMIT: 500,
  MAX_CURSOR_LIMIT: 3000,
  MIN_VALID_VIDEOS_THRESHOLD: 10, // Minimum valid videos needed before stopping

  // Parallel processing parameters
  MAX_CONCURRENT_JOBS: 4,

  // Filtering parameters
  MAX_VIDEOS_FOR_CONTEXT: 10,
  CREATOR_BATCH_SIZE: 10,

  // S3 Upload parameters
  DEFAULT_MAX_UPLOAD_CONCURRENT_JOBS: 10,

  // Retry and service rotation parameters
  MAX_RETRIES: 3,
  BASE_DELAY_MS: 1000, // 1 second
  MAX_DELAY_MS: 30000, // 30 seconds
  RATE_LIMIT_COOLDOWN_MS: 5 * 60 * 1000, // 5 minutes
} as const;

/**
 * Extract essential video data for workflow processing (reduce payload size)
 * @param video Full video object
 * @returns Minimal video data with essential fields
 */
function extractVideoForWorkflow(video: TiktokVideoSchema) {
  return {
    video_id: video.video_id,
    description: video.description, // Contains title and hashtags
    view_count: video.view_count,
    like_count: video.like_count,
    comment_count: video.comment_count,
    share_count: video.share_count,
    thumbnail_url: video.thumbnail_url,
    author: {
      unique_id: video.author.unique_id,
      nickname: video.author.nickname,
      follower_count: video.author.follower_count,
    },
  };
}

/**
 * Service for scouting videos from different platforms and updating the database
 */
export class VideoScouterService {
  private serviceMap: Record<string, TiktokServiceSchema>;
  private serviceConfigs: Record<string, ServiceConfig>;
  private preferredService?: string;

  constructor() {
    // Initialize all available TikTok services
    const tokAPIService = new TokAPIService();
    // const tikHubService = new TikHubService();

    this.serviceMap = {
      tokapi: tokAPIService,
      // tikhub: tikHubService,
    };

    // Initialize service configurations with weights
    this.serviceConfigs = {
      tokapi: {
        name: 'TokAPI',
        weight: 1,
        cooldownMs: SCOUTING_CONFIG.RATE_LIMIT_COOLDOWN_MS,
        consecutiveFailures: 0,
        maxConsecutiveFailures: 10,
      },
      // tikhub: {
      //   name: 'TikHub',
      //   weight: 0,
      //   cooldownMs: SCOUTING_CONFIG.RATE_LIMIT_COOLDOWN_MS,
      //   consecutiveFailures: 0,
      //   maxConsecutiveFailures: 3,
      // },
    };

    console.log('TikTok services initialized with weighted rotation:');
    this.logServiceStatus();
  }

  /**
   * Set a preferred service to use instead of rotating
   * @param serviceKey The service key to prefer ('tokapi' or 'tikhub'), or null to use rotation
   */
  setPreferredService(serviceKey: string | null) {
    if (serviceKey && !this.serviceMap[serviceKey]) {
      throw new Error(
        `Invalid service key: ${serviceKey}. Available services: ${Object.keys(this.serviceMap).join(', ')}`,
      );
    }
    this.preferredService = serviceKey || undefined;
    console.log(
      this.preferredService
        ? `Preferred service set to: ${this.serviceConfigs[this.preferredService].name}`
        : 'Service rotation mode enabled',
    );
  }

  /**
   * Get the currently preferred service if any
   * @returns The preferred service key or null if using rotation
   */
  getPreferredService(): string | null {
    return this.preferredService || null;
  }

  /**
   * Select the best available service based on weights and failure history
   * @returns The selected service and its key
   */
  private selectService(): {
    service: TiktokServiceSchema;
    serviceKey: string;
  } {
    // If a preferred service is set and available, use it
    if (this.preferredService && this.serviceMap[this.preferredService]) {
      const config = this.serviceConfigs[this.preferredService];
      const now = Date.now();

      // Check if preferred service is available (not in cooldown or failed too many times)
      const inCooldown =
        config.lastFailureTime &&
        now - config.lastFailureTime < config.cooldownMs;
      const tooManyFailures =
        config.consecutiveFailures >= config.maxConsecutiveFailures;

      if (!inCooldown && !tooManyFailures) {
        const service = this.serviceMap[this.preferredService];
        return { service, serviceKey: this.preferredService };
      } else {
        console.log(
          `Preferred service ${config.name} unavailable (${inCooldown ? 'cooldown' : 'too many failures'}), falling back to rotation`,
        );
      }
    }

    const now = Date.now();
    const availableServices: Array<{
      key: string;
      config: ServiceConfig;
      adjustedWeight: number;
    }> = [];

    // Check each service's availability and calculate adjusted weights
    for (const [key, config] of Object.entries(this.serviceConfigs)) {
      // Skip if service is in cooldown period
      if (
        config.lastFailureTime &&
        now - config.lastFailureTime < config.cooldownMs
      ) {
        const remainingCooldown = Math.ceil(
          (config.cooldownMs - (now - config.lastFailureTime)) / 1000,
        );
        console.log(
          `Service ${config.name} in cooldown for ${remainingCooldown}s`,
        );
        continue;
      }

      // Skip if service has too many consecutive failures
      if (config.consecutiveFailures >= config.maxConsecutiveFailures) {
        console.log(
          `Service ${config.name} disabled due to ${config.consecutiveFailures} consecutive failures`,
        );
        continue;
      }

      // Calculate adjusted weight based on failure history
      let adjustedWeight = config.weight;
      if (config.consecutiveFailures > 0) {
        adjustedWeight *= Math.pow(0.5, config.consecutiveFailures); // Reduce weight exponentially
      }

      availableServices.push({ key, config, adjustedWeight });
    }

    // If no services available, reset all failure counters and try again
    if (availableServices.length === 0) {
      console.warn('All services unavailable, resetting failure counters');
      for (const config of Object.values(this.serviceConfigs)) {
        config.consecutiveFailures = 0;
        config.lastFailureTime = undefined;
      }
      return this.selectService(); // Recursive call after reset
    }

    // Select service using weighted random selection
    const totalWeight = availableServices.reduce(
      (sum, s) => sum + s.adjustedWeight,
      0,
    );
    let random = Math.random() * totalWeight;

    for (const serviceInfo of availableServices) {
      random -= serviceInfo.adjustedWeight;
      if (random <= 0) {
        const service = this.serviceMap[serviceInfo.key];
        // console.log(
        //   `Selected service: ${serviceInfo.config.name} (weight: ${serviceInfo.adjustedWeight.toFixed(2)})`,
        // );
        return { service, serviceKey: serviceInfo.key };
      }
    }

    // Fallback to first available service
    const fallback = availableServices[0];
    const service = this.serviceMap[fallback.key];
    console.log(`Fallback to: ${fallback.config.name}`);
    return { service, serviceKey: fallback.key };
  }

  /**
   * Mark a service as failed and adjust its configuration
   * @param serviceKey The service key that failed
   * @param isRateLimit Whether the failure was due to rate limiting
   */
  private markServiceFailure(serviceKey: string, isRateLimit: boolean = false) {
    const config = this.serviceConfigs[serviceKey];
    if (!config) return;

    config.consecutiveFailures++;

    if (isRateLimit) {
      config.lastFailureTime = Date.now();
      console.log(
        `Service ${config.name} hit rate limit, entering cooldown period`,
      );
    }

    console.log(
      `Service ${config.name} failure count: ${config.consecutiveFailures}/${config.maxConsecutiveFailures}`,
    );
  }

  /**
   * Mark a service as successful and reset its failure counter
   * @param serviceKey The service key that succeeded
   */
  private markServiceSuccess(serviceKey: string) {
    const config = this.serviceConfigs[serviceKey];
    if (!config) return;

    if (config.consecutiveFailures > 0) {
      console.log(`Service ${config.name} recovered, resetting failure count`);
      config.consecutiveFailures = 0;
    }
  }

  /**
   * Log current status of all services
   */
  private logServiceStatus() {
    const now = Date.now();
    console.log('Service Status:');
    for (const [, config] of Object.entries(this.serviceConfigs)) {
      const cooldownRemaining = config.lastFailureTime
        ? Math.max(0, config.cooldownMs - (now - config.lastFailureTime))
        : 0;
      console.log(
        `  ${config.name}: weight=${config.weight}, failures=${config.consecutiveFailures}/${config.maxConsecutiveFailures}, cooldown=${Math.ceil(cooldownRemaining / 1000)}s`,
      );
    }
  }

  /**
   * Sleep for specified milliseconds
   * @param ms Milliseconds to sleep
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Calculate delay for exponential backoff
   * @param attempt Current attempt number (0-based)
   * @returns Delay in milliseconds
   */
  private calculateBackoffDelay(attempt: number): number {
    const baseDelay = SCOUTING_CONFIG.BASE_DELAY_MS;
    const maxDelay = SCOUTING_CONFIG.MAX_DELAY_MS;

    // Exponential backoff with jitter
    const exponentialDelay = baseDelay * Math.pow(2, attempt);
    const jitter = Math.random() * 0.1 * exponentialDelay;

    return Math.min(exponentialDelay + jitter, maxDelay);
  }

  /**
   * Check if error is rate limit related
   * @param error The error to check
   * @returns True if it's a rate limit error
   */
  private isRateLimitError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return (
      message.includes('rate limit') ||
      message.includes('429') ||
      message.includes('too many requests')
    );
  }

  /**
   * Execute a service method with retry logic, service rotation, and error handling
   * @param operation Name of the operation for logging
   * @param serviceMethod Function that takes a service and returns a promise
   * @returns The result of the service method
   */
  private async executeServiceMethod<T>(
    operation: string,
    serviceMethod: (service: TiktokServiceSchema) => Promise<T>,
  ): Promise<T> {
    let lastError: Error = new Error('No attempts made');
    const maxRetries = SCOUTING_CONFIG.MAX_RETRIES;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      const { service, serviceKey } = this.selectService();
      const serviceName = this.serviceConfigs[serviceKey].name;

      try {
        if (attempt > 0) {
          console.log(
            `${operation} - Attempt ${attempt + 1}/${maxRetries + 1} with ${serviceName}`,
          );
        }

        const result = await serviceMethod(service);

        // Mark service as successful
        this.markServiceSuccess(serviceKey);

        if (attempt > 0) {
          console.log(
            `${operation} succeeded after ${attempt + 1} attempts with ${serviceName}`,
          );
        }

        return result;
      } catch (error) {
        lastError = error as Error;
        const isRateLimit = this.isRateLimitError(lastError);

        // Mark service as failed
        this.markServiceFailure(serviceKey, isRateLimit);

        console.error(
          `${operation} failed (attempt ${attempt + 1}/${maxRetries + 1}) with ${serviceName}:`,
          lastError.message,
        );

        // If this is the last attempt, don't wait
        if (attempt === maxRetries) {
          break;
        }

        // Calculate backoff delay
        const delay = this.calculateBackoffDelay(attempt);
        console.log(`Retrying in ${delay}ms...`);
        await this.sleep(delay);
      }
    }

    // All retries failed
    console.error(
      `${operation} failed after ${maxRetries + 1} attempts across all services`,
    );
    this.logServiceStatus();

    throw new Error(
      `${operation} failed after ${maxRetries + 1} attempts: ${lastError.message}`,
    );
  }

  /**
   * Get information about the current service configuration
   * @returns Service configuration info
   */
  getServiceInfo() {
    const now = Date.now();
    const serviceStatus = Object.entries(this.serviceConfigs).map(
      ([key, config]) => ({
        key,
        name: config.name,
        weight: config.weight,
        consecutiveFailures: config.consecutiveFailures,
        maxConsecutiveFailures: config.maxConsecutiveFailures,
        inCooldown: config.lastFailureTime
          ? now - config.lastFailureTime < config.cooldownMs
          : false,
        cooldownRemainingMs: config.lastFailureTime
          ? Math.max(0, config.cooldownMs - (now - config.lastFailureTime))
          : 0,
      }),
    );

    return {
      totalServices: Object.keys(this.serviceMap).length,
      availableServices: Object.keys(this.serviceMap),
      preferredService: this.preferredService || null,
      serviceStatus,
      retryConfig: {
        maxRetries: SCOUTING_CONFIG.MAX_RETRIES,
        baseDelayMs: SCOUTING_CONFIG.BASE_DELAY_MS,
        maxDelayMs: SCOUTING_CONFIG.MAX_DELAY_MS,
        rateLimitCooldownMs: SCOUTING_CONFIG.RATE_LIMIT_COOLDOWN_MS,
      },
    };
  }

  /**
   * Search for TikTok videos directly
   * @param keyword The keyword to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   * @param sort_type Sort type (0: relevance, 1: most liked)
   * @param publish_time Publish time (0: unlimited, 1: last 24 hours, 7: past week, 30: past month, 90: past 3 months, 180: past 6 months)
   * @param region Region code (optional)
   */
  async searchTiktokVideos(
    keyword: string,
    offset = 0,
    count = 25,
    sort_type = 0,
    publish_time = 0,
    region?: string,
  ) {
    return this.executeServiceMethod(
      `Searching TikTok videos for keyword: "${keyword}"`,
      (service) =>
        service.searchVideos(
          keyword,
          offset,
          count,
          sort_type,
          publish_time,
          region,
        ),
    );
  }

  /**
   * Search for TikTok challenges/hashtags
   * @param keyword The keyword to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async searchTiktokChallenges(keyword: string, offset = 0, count = 20) {
    return this.executeServiceMethod(
      `Searching TikTok challenges for keyword: "${keyword}"`,
      (service) => service.searchHashtag(keyword, offset, count),
    );
  }

  /**
   * Extract URLs from videos for S3 upload
   * @param videos Array of videos to extract URLs from
   * @returns Array of file upload data
   */
  private extractUrlsForUpload(videos: TiktokVideoSchema[]): FileUploadData[] {
    const urlsToUpload: FileUploadData[] = [];

    for (const video of videos) {
      // Add thumbnail URL if exists
      if (video.thumbnail_url) {
        urlsToUpload.push({
          url: video.thumbnail_url,
          type: 'thumbnail',
          metadata: {
            video_id: video.video_id,
            author_id: video.author.unique_id || video.author.uid || '',
          },
        });
      }

      // Add author avatar URL if exists
      if (video.author.avatar_url) {
        urlsToUpload.push({
          url: video.author.avatar_url,
          type: 'avatar',
          metadata: {
            author_id: video.author.unique_id || video.author.uid || '',
            author_nickname: video.author.nickname || '',
          },
        });
      }

      // Add video source URL if exists
      if (video.video_url) {
        urlsToUpload.push({
          url: video.video_url,
          type: 'video',
          metadata: {
            video_id: video.video_id,
            author_id: video.author.unique_id || video.author.uid || '',
          },
        });
      }
    }

    return urlsToUpload;
  }

  /**
   * Replace URLs in videos with S3 URLs based on upload results
   * @param videos Array of videos to update
   * @param uploadResults Array of upload results
   * @returns Updated videos with S3 URLs
   */
  private replaceUrlsWithS3(
    videos: TiktokVideoSchema[],
    uploadResults: UploadResult[],
  ): TiktokVideoSchema[] {
    // Create a map of original URLs to S3 URLs for quick lookup
    const urlMap = new Map<string, string>();

    for (const result of uploadResults) {
      if (result.success && result.s3Url) {
        urlMap.set(result.originalUrl, result.s3Url);
      }
    }

    // Update videos with S3 URLs
    return videos.map((video) => {
      const updatedVideo = { ...video };

      // Replace thumbnail URL
      if (video.thumbnail_url && urlMap.has(video.thumbnail_url)) {
        updatedVideo.thumbnail_url = urlMap.get(video.thumbnail_url)!;
      }

      // Replace author avatar URL
      if (video.author.avatar_url && urlMap.has(video.author.avatar_url)) {
        updatedVideo.author = {
          ...updatedVideo.author,
          avatar_url: urlMap.get(video.author.avatar_url)!,
        };
      }

      // Replace video source URL
      if (video.video_url && urlMap.has(video.video_url)) {
        updatedVideo.video_url = urlMap.get(video.video_url)!;
      }

      return updatedVideo;
    });
  }

  /**
   * Upload URLs to S3 and replace them in videos
   * @param videos Array of videos to process
   * @returns Videos with S3 URLs and upload statistics
   */
  private async uploadAndReplaceUrls(videos: TiktokVideoSchema[]): Promise<{
    updatedVideos: TiktokVideoSchema[];
    uploadStats: {
      totalUrls: number;
      successfulUploads: number;
      failedUploads: number;
      uploadResults: UploadResult[];
    };
  }> {
    console.log('Starting URL upload and replacement process...');

    // Extract URLs for upload
    const urlsToUpload = this.extractUrlsForUpload(videos);
    console.log(`Found ${urlsToUpload.length} URLs to upload to S3`);

    if (urlsToUpload.length === 0) {
      console.log('No URLs found for upload, skipping S3 upload process');
      return {
        updatedVideos: videos,
        uploadStats: {
          totalUrls: 0,
          successfulUploads: 0,
          failedUploads: 0,
          uploadResults: [],
        },
      };
    }

    // Remove duplicates based on URL
    const uniqueUrls = urlsToUpload.filter(
      (item, index, self) =>
        index === self.findIndex((u) => u.url === item.url),
    );
    console.log(`Deduplicated to ${uniqueUrls.length} unique URLs`);

    // Upload URLs to S3 in parallel
    const uploadResults = await s3Service.uploadFilesInParallel(uniqueUrls);

    // Calculate statistics
    const successfulUploads = uploadResults.filter((r) => r.success).length;
    const failedUploads = uploadResults.length - successfulUploads;

    console.log(
      `Upload completed: ${successfulUploads} successful, ${failedUploads} failed`,
    );

    // Replace URLs in videos with S3 URLs
    const updatedVideos = this.replaceUrlsWithS3(videos, uploadResults);

    return {
      updatedVideos,
      uploadStats: {
        totalUrls: urlsToUpload.length,
        successfulUploads,
        failedUploads,
        uploadResults,
      },
    };
  }

  /**
   * Process videos and save them to the database
   * @param videos Array of videos to process
   * @param skipOssUpload Whether to skip uploading to S3 (default: true)
   * @returns Array of processed video IDs and upload statistics
   */
  private async processVideos(
    videos: TiktokVideoSchema[],
    skipOssUpload = true,
  ): Promise<{
    processedVideoIds: number[];
    uploadStats?: {
      totalUrls: number;
      successfulUploads: number;
      failedUploads: number;
      uploadResults: UploadResult[];
    };
  }> {
    console.log(`Processing ${videos.length} videos...`);

    let videosToProcess = videos;
    let uploadStats;

    if (!skipOssUpload) {
      // Upload URLs to S3 and replace them
      const uploadResult = await this.uploadAndReplaceUrls(videos);
      videosToProcess = uploadResult.updatedVideos;
      uploadStats = uploadResult.uploadStats;
      console.log(`Processed ${videosToProcess.length} videos with S3 URLs.`);
    } else {
      console.log('Skipping S3 upload as per configuration.');
    }

    // Process videos and save to database
    const processedVideoIds: number[] = [];

    for (const video of videosToProcess) {
      try {
        const videoId = await scoutDbService.processTikTokVideo(video);
        processedVideoIds.push(videoId);
        // console.log('Video processed video id:', videoId);
      } catch (error) {
        console.error('Error processing video:', error);
        // Continue with the next video
      }
    }

    console.log(`Saved ${processedVideoIds.length} videos to database.`);

    return {
      processedVideoIds,
      uploadStats,
    };
  }

  /**
   * Get videos for a specific TikTok hashtag and update the database
   * @param challengeId The challenge/hashtag ID
   * @param cursor Pagination cursor
   * @param count Number of results to return
   * @param skipOssUpload Whether to skip uploading to S3 (default: true)
   * @param region Region code (optional)
   */
  async scoutTiktokHashtagVideos(
    challengeId: string,
    cursor = 0,
    count = 20,
    skipOssUpload = true,
    region?: string,
  ): Promise<{
    videos: TiktokVideoSchema[];
    processedVideoIds: number[];
    uploadStats?: {
      totalUrls: number;
      successfulUploads: number;
      failedUploads: number;
      uploadResults: UploadResult[];
    };
  }> {
    // Get videos from the service
    const videos = await this.executeServiceMethod(
      `Getting hashtag videos for challenge ID: "${challengeId}"`,
      (service) => service.getHashtagVideos(challengeId, cursor, count, region),
    );

    // Process videos and save to database
    const processResult = await this.processVideos(videos, skipOssUpload);

    console.log(
      `Video processing complete. Processed ${processResult.processedVideoIds.length} videos.`,
    );

    return {
      videos,
      processedVideoIds: processResult.processedVideoIds,
      uploadStats: processResult.uploadStats,
    };
  }

  /**
   * Search for challenges based on keywords and find the best ones by view count
   * @param keywords Array of keywords to search for
   * @param traceId Workflow run ID for context tracking
   * @param minChallenges Minimum number of challenges to find
   */
  async findBestChallenges(
    keywords: string[],
    traceId: string,
    minChallenges = 5,
  ) {
    console.log(`Finding best challenges for keywords: ${keywords.join(', ')}`);

    const allChallenges: TiktokChallengeSchema[] = [];

    // Search for challenges for each keyword
    for (const keyword of keywords) {
      try {
        const challenges = await this.searchTiktokChallenges(keyword, 0, 20);
        console.log(
          `Found ${challenges.length} challenges for keyword "${keyword}"`,
        );
        allChallenges.push(...challenges);
      } catch (error) {
        console.error(
          `Error searching challenges for keyword "${keyword}":`,
          error,
        );
      }
    }

    // Sort challenges by view count (descending)
    const sortedChallenges = allChallenges
      .sort((a, b) => b.view_count - a.view_count)
      // Remove duplicates based on challenge_id
      .filter(
        (challenge, index, self) =>
          index ===
          self.findIndex((c) => c.challenge_id === challenge.challenge_id),
      );

    // Take at least minChallenges or all if fewer are available
    const bestChallenges = sortedChallenges.slice(
      0,
      Math.max(minChallenges, sortedChallenges.length),
    );

    console.log(`Selected ${bestChallenges.length} best challenges`);

    // Save to context
    const contextData = {
      keywords,
      challenges: bestChallenges,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      traceId,
      'best_challenges',
      contextData,
    );

    return bestChallenges;
  }

  /**
   * Fetch videos from the best challenges and collect at least 100 good videos
   * @param traceId Workflow run ID for context tracking
   * @param minVideos Minimum number of videos to collect (default: 100)
   */
  async collectGoodVideos(
    traceId: string,
    minVideos = 100,
  ): Promise<{
    videos: TiktokVideoSchema[];
    processedVideoIds: number[];
    totalVideos: number;
  }> {
    console.log(
      `Collecting at least ${minVideos} good videos for trace ID: ${traceId}`,
    );

    // Get challenges from context
    const contextData = await workflowDbService.getWorkflowContext(
      traceId,
      'best_challenges',
    );

    if (!contextData || !contextData.contextData) {
      throw new Error(
        'No challenges found in context. Run findBestChallenges first.',
      );
    }

    const contextDataObj = contextData.contextData as {
      challenges: TiktokChallengeSchema[];
    };

    if (
      !contextDataObj.challenges ||
      !Array.isArray(contextDataObj.challenges)
    ) {
      throw new Error('Invalid challenge data in context');
    }

    const challenges = contextDataObj.challenges;
    console.log(`Found ${challenges.length} challenges in context`);

    const allVideos: TiktokVideoSchema[] = [];
    const processedVideoIds: number[] = [];

    // Fetch videos from each challenge until we have enough
    for (const challenge of challenges) {
      if (allVideos.length >= minVideos) {
        break;
      }

      try {
        console.log(
          `Fetching videos for challenge: ${challenge.challenge_name}`,
        );
        // Get more videos per challenge to reach our target faster
        const result = await this.scoutTiktokHashtagVideos(
          challenge.challenge_id,
          0,
          25,
        );

        allVideos.push(...result.videos);
        processedVideoIds.push(...result.processedVideoIds);

        console.log(`Collected ${allVideos.length}/${minVideos} videos so far`);
      } catch (error) {
        console.error(
          `Error fetching videos for challenge ${challenge.challenge_name}:`,
          error,
        );
      }
    }

    // Sort videos by view count (descending)
    const sortedVideos = allVideos.sort((a, b) => b.view_count - a.view_count);

    // Save to context
    const contextData2 = {
      challenges,
      videos: sortedVideos.map((video) => ({
        video_id: video.video_id,
        title: video.title,
        view_count: video.view_count,
        like_count: video.like_count,
        comment_count: video.comment_count,
        author: {
          unique_id: video.author.unique_id,
          nickname: video.author.nickname,
        },
      })),
      processedVideoIds,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      traceId,
      'good_videos',
      contextData2,
    );

    return {
      videos: sortedVideos,
      processedVideoIds,
      totalVideos: sortedVideos.length,
    };
  }

  /**
   * Utility function to process tasks in parallel with a maximum concurrency limit
   * @param tasks Array of async functions to execute
   * @param maxConcurrency Maximum number of concurrent tasks
   * @returns Array of results from Promise.allSettled
   */
  private async processInParallel<T>(
    tasks: (() => Promise<T>)[],
    maxConcurrency: number = SCOUTING_CONFIG.MAX_CONCURRENT_JOBS,
  ): Promise<PromiseSettledResult<T>[]> {
    const results: PromiseSettledResult<T>[] = [];

    for (let i = 0; i < tasks.length; i += maxConcurrency) {
      const batch = tasks.slice(i, i + maxConcurrency);
      const batchResults = await Promise.allSettled(
        batch.map((task) => task()),
      );
      results.push(...batchResults);

      // Log progress
      console.log(
        `Processed batch ${Math.floor(i / maxConcurrency) + 1}/${Math.ceil(tasks.length / maxConcurrency)}`,
      );
    }

    return results;
  }

  /**
   * Shortcut function to scout creators and their videos from a challenge
   * @param challengeId The challenge/hashtag ID to scout
   * @param desiredCreatorCount Number of unique creators to collect
   * @param filters Optional filtering conditions for videos and creators
   * @param skipVideoIds Array of video IDs to skip when finding videos under a challenge (default: empty array)
   * @param startCursor Starting cursor position for video fetching to get varied results (default: 0)
   * @param skipOssUpload Whether to skip uploading to S3 (default: true)
   * @param skipCreatorIds Array of creator IDs to skip when scouting (default: empty array)
   * @param concurrentTasksLimit Maximum number of concurrent creator post fetching tasks (default: 4)
   * @param region Region code (optional)
   * @returns Object containing creators and their videos
   */
  async scoutChallengeCreators(
    challengeId: string,
    desiredCreatorCount: number,
    filters?: {
      // Video filtering conditions (ignore when value is 0)
      minViews?: number;
      minLikes?: number;
      minComments?: number;
      // Creator filtering conditions (ignore when value is 0)
      minFollowers?: number;
      minRecentMedianViews?: number;
      minRecentMedianComments?: number;
      minRecentMedianLikes?: number;
      minRecentAverageViews?: number;
      minRecentAverageComments?: number;
      minRecentAverageLikes?: number;
    },
    skipVideoIds: string[] = [],
    startCursor: number = 0,
    skipOssUpload = true,
    skipCreatorIds: string[] = [],
    concurrentTasksLimit: number = SCOUTING_CONFIG.MAX_CONCURRENT_JOBS,
    region?: string,
  ) {
    console.log(`Starting challenge scouting for challenge ID: ${challengeId}`);
    console.log(`Target: ${desiredCreatorCount} unique creators`);
    if (skipVideoIds.length > 0) {
      console.log(`Skipping ${skipVideoIds.length} specific video IDs`);
    }
    if (skipCreatorIds.length > 0) {
      console.log(`Skipping ${skipCreatorIds.length} already scouted creators`);
    }
    if (startCursor > 0) {
      console.log(`Starting from cursor position: ${startCursor}`);
    }

    // Step 1: Search challenge videos with enhanced mechanism
    console.log('Step 1: Fetching challenge videos with guaranteed results...');
    const challengeVideos: TiktokVideoSchema[] = [];
    let cursor = startCursor;
    const videosPerPage = SCOUTING_CONFIG.VIDEOS_PER_API_PAGE;
    const maxVideos = SCOUTING_CONFIG.MAX_VIDEO_PER_CHALLENGE;
    const maxCursorLimit = SCOUTING_CONFIG.MAX_CURSOR_LIMIT;
    const minValidThreshold = SCOUTING_CONFIG.MIN_VALID_VIDEOS_THRESHOLD;

    // Enhanced video fetching with guaranteed results mechanism
    let totalAttempts = 0;
    let consecutiveEmptyBatches = 0;
    const maxConsecutiveEmpty = 3; // Stop after 3 consecutive empty batches

    while (challengeVideos.length < maxVideos && cursor < maxCursorLimit) {
      try {
        totalAttempts++;
        const remainingVideos = maxVideos - challengeVideos.length;
        const countToFetch = Math.min(videosPerPage, remainingVideos);

        console.log(
          `Fetching batch ${totalAttempts}: cursor=${cursor}, count=${countToFetch}, collected=${challengeVideos.length}/${maxVideos}`,
        );

        const videos = await this.executeServiceMethod(
          `Getting hashtag videos batch (cursor: ${cursor}), challengeId: ${challengeId}`,
          (service) =>
            service.getHashtagVideos(challengeId, cursor, countToFetch, region),
        );

        if (videos.length === 0) {
          consecutiveEmptyBatches++;
          console.log(
            `Empty batch ${consecutiveEmptyBatches}/${maxConsecutiveEmpty} at cursor ${cursor}`,
          );

          if (consecutiveEmptyBatches >= maxConsecutiveEmpty) {
            console.log('Reached maximum consecutive empty batches, stopping');
            break;
          }

          // Skip ahead to try different cursor positions
          cursor += videosPerPage;
          continue;
        }

        // Reset consecutive empty counter on successful fetch
        consecutiveEmptyBatches = 0;

        // Apply video filters if specified (ignore when value is 0)
        const filteredVideos = videos.filter((video) => {
          // Skip videos with IDs in the skipVideoIds array
          if (
            skipVideoIds.length > 0 &&
            skipVideoIds.includes(video.video_id)
          ) {
            return false;
          }

          if (
            filters?.minViews &&
            filters.minViews > 0 &&
            video.view_count < filters.minViews
          ) {
            return false;
          }
          if (
            filters?.minLikes &&
            filters.minLikes > 0 &&
            video.like_count < filters.minLikes
          ) {
            return false;
          }
          if (
            filters?.minComments &&
            filters.minComments > 0 &&
            video.comment_count < filters.minComments
          ) {
            return false;
          }
          return true;
        });

        challengeVideos.push(...filteredVideos);
        cursor += videos.length;

        const skippedCount = videos.length - filteredVideos.length;
        console.log(
          `Batch ${totalAttempts}: ${filteredVideos.length}/${videos.length} passed filters, ${skippedCount} skipped. Total: ${challengeVideos.length}/${maxVideos}`,
        );

        // If we have enough valid videos and meet minimum threshold, we can be more selective
        if (
          challengeVideos.length >= minValidThreshold &&
          filteredVideos.length === 0
        ) {
          console.log(
            `Have ${challengeVideos.length} valid videos (>= ${minValidThreshold}), skipping low-quality batches`,
          );
          cursor += videosPerPage; // Skip ahead faster when we have enough
        }
      } catch (error) {
        console.error(`Error fetching videos at cursor ${cursor}:`, error);
        cursor += videosPerPage; // Skip ahead on error

        // If we have some videos, continue; otherwise break
        if (challengeVideos.length === 0) {
          break;
        }
      }
    }

    // Enhanced completion logging
    const finalCursor = cursor;
    const reachedCursorLimit = finalCursor >= maxCursorLimit;

    console.log(
      `Step 1 complete: Collected ${challengeVideos.length} challenge videos after ${totalAttempts} attempts`,
    );
    console.log(
      `Final cursor position: ${finalCursor}/${maxCursorLimit} ${reachedCursorLimit ? '(LIMIT REACHED)' : ''}`,
    );

    if (challengeVideos.length < maxVideos) {
      console.log(
        `Note: Collected ${challengeVideos.length}/${maxVideos} videos. ${reachedCursorLimit ? 'Cursor limit reached.' : 'API exhausted or filters too strict.'}`,
      );
    }

    // Step 2: Extract unique creators with campaign-aware deduplication
    console.log('Step 2: Extracting unique creators...');
    const creatorMap = new Map<string, TiktokVideoSchema['author']>();

    for (const video of challengeVideos) {
      const creatorId = video.author.sec_uid || video.author.uid;

      if (creatorId && !creatorMap.has(creatorId)) {
        creatorMap.set(creatorId, video.author);
      }
    }

    let uniqueCreators = Array.from(creatorMap.values());
    console.log(`Found ${uniqueCreators.length} unique creators from videos`);

    // Campaign-aware creator filtering: remove already scouted creators
    if (skipCreatorIds.length > 0) {
      const beforeCount = uniqueCreators.length;
      const skipCreatorSet = new Set(skipCreatorIds);

      uniqueCreators = uniqueCreators.filter(
        (creator) => !skipCreatorSet.has(creator.unique_id),
      );

      if (beforeCount !== uniqueCreators.length) {
        console.log(
          `🔄 Campaign deduplication: ${beforeCount - uniqueCreators.length} creators filtered out (${uniqueCreators.length} remaining)`,
        );
      }
    }

    uniqueCreators.sort((a, b) => a.follower_count - b.follower_count);

    // Limit to desired count
    const creatorsToProcess = uniqueCreators.slice(0, desiredCreatorCount);
    console.log(
      `Processing ${creatorsToProcess.length} creators (limited to desired count: ${desiredCreatorCount})`,
    );

    // Step 3: Fetch creator posts in parallel with concurrent limit
    console.log(
      `Step 3: Fetching creator posts in parallel (concurrency: ${concurrentTasksLimit})...`,
    );

    // Create tasks for parallel processing
    const creatorTasks = creatorsToProcess.map(
      (creator) => () => this.fetchCreatorPostsWithRetry(creator),
    );

    // Process creator post fetching in parallel with concurrent limit
    const creatorPostResults = await this.processInParallel(
      creatorTasks,
      concurrentTasksLimit,
    );

    // Process results
    const successfulCreators: Array<{
      creator: TiktokVideoSchema['author'];
      videos: TiktokVideoSchema[];
    }> = [];

    const allCreatorVideos: Array<TiktokVideoSchema> = [];

    for (let i = 0; i < creatorPostResults.length; i++) {
      const result = creatorPostResults[i];
      if (result.status === 'fulfilled' && result.value) {
        const { creator, videos } = result.value;
        successfulCreators.push({ creator, videos });
        allCreatorVideos.push(...videos);
      } else {
        console.error(
          `Failed to fetch posts for creator ${i + 1}:`,
          result.status === 'rejected' ? result.reason : 'Unknown error',
        );
      }
    }

    console.log(
      `Successfully fetched posts for ${successfulCreators.length}/${creatorsToProcess.length} creators`,
    );
    console.log(`Total creator videos collected: ${allCreatorVideos.length}`);

    // Step 4: Process and persist all videos
    console.log('Step 4: Processing and persisting videos...');
    const allVideos = [...challengeVideos, ...allCreatorVideos];
    const processResult = await this.processVideos(allVideos, skipOssUpload);

    console.log(
      `Persisted ${processResult.processedVideoIds.length} videos to database`,
    );

    if (processResult.uploadStats && !skipOssUpload) {
      console.log(
        `S3 Upload Stats: ${processResult.uploadStats.successfulUploads}/${processResult.uploadStats.totalUrls} successful`,
      );
    }

    // Step 5: Apply creator filtering if specified
    console.log('Step 5: Applying creator filters...(except min followers)');
    let filteredCreators = successfulCreators;

    if (filters) {
      filteredCreators = successfulCreators.filter(({ videos }) => {
        // Apply recent video metrics filters (ignore when value is 0)
        if (
          videos.length > 0 &&
          (filters.minRecentMedianViews ||
            filters.minRecentMedianComments ||
            filters.minRecentMedianLikes)
        ) {
          const views = videos.map((v) => v.view_count || 0);
          const comments = videos.map((v) => v.comment_count || 0);
          const likes = videos.map((v) => v.like_count || 0);

          // Calculate median values
          const medianViews = this.calculateMedian(views);
          const medianComments = this.calculateMedian(comments);
          const medianLikes = this.calculateMedian(likes);

          if (
            filters.minRecentMedianViews &&
            filters.minRecentMedianViews > 0 &&
            medianViews < filters.minRecentMedianViews
          ) {
            return false;
          }
          if (
            filters.minRecentMedianComments &&
            filters.minRecentMedianComments > 0 &&
            medianComments < filters.minRecentMedianComments
          ) {
            return false;
          }
          if (
            filters.minRecentMedianLikes &&
            filters.minRecentMedianLikes > 0 &&
            medianLikes < filters.minRecentMedianLikes
          ) {
            return false;
          }
        }

        // Apply recent video average metrics filters (ignore when value is 0)
        if (
          videos.length > 0 &&
          (filters.minRecentAverageViews ||
            filters.minRecentAverageComments ||
            filters.minRecentAverageLikes)
        ) {
          const views = videos.map((v) => v.view_count || 0);
          const comments = videos.map((v) => v.comment_count || 0);
          const likes = videos.map((v) => v.like_count || 0);

          // Calculate average values
          const averageViews = this.calculateAverage(views);
          const averageComments = this.calculateAverage(comments);
          const averageLikes = this.calculateAverage(likes);

          if (
            filters.minRecentAverageViews &&
            filters.minRecentAverageViews > 0 &&
            averageViews < filters.minRecentAverageViews
          ) {
            return false;
          }
          if (
            filters.minRecentAverageComments &&
            filters.minRecentAverageComments > 0 &&
            averageComments < filters.minRecentAverageComments
          ) {
            return false;
          }
          if (
            filters.minRecentAverageLikes &&
            filters.minRecentAverageLikes > 0 &&
            averageLikes < filters.minRecentAverageLikes
          ) {
            return false;
          }
        }

        return true;
      });

      console.log(
        `Creator filtering: ${filteredCreators.length}/${successfulCreators.length} creators passed filters`,
      );
    }

    // Step 6: Prepare final results with reliable creator data
    console.log('Step 6: Preparing final results...');
    const finalCreators = filteredCreators
      .map(({ creator, videos }) => {
        // Use creator data from the first video post (more reliable)
        const reliableCreatorData =
          videos.length > 0 ? videos[0].author : creator;

        return {
          ...reliableCreatorData,
          videoCount: videos.length,
        };
      })
      .filter((creator) => {
        if (!filters) return true;

        if (
          filters.minFollowers &&
          filters.minFollowers > 0 &&
          creator.follower_count < filters.minFollowers
        ) {
          return false;
        }

        return true;
      });

    console.log(
      `Filtered creators with valid metrics: ${finalCreators.length}/${filteredCreators.length} creators passed reliability filters`,
    );

    // Optimize video data for workflow processing (reduce payload size)
    const optimizedVideos = allCreatorVideos.map(extractVideoForWorkflow);

    const result = {
      creators: finalCreators,
      videos: optimizedVideos, // Return optimized video data for workflow
      stats: {
        challengeVideosCollected: challengeVideos.length,
        uniqueCreatorsFound: uniqueCreators.length,
        creatorsProcessed: creatorsToProcess.length,
        successfulCreators: successfulCreators.length,
        totalCreatorVideos: allCreatorVideos.length,
        totalVideosProcessed: processResult.processedVideoIds.length,
        // Enhanced cursor stats
        startCursor,
        finalCursor,
        totalAttempts,
        cursorLimitReached: reachedCursorLimit,
        maxCursorLimit,
      },
    };

    console.log('Challenge scouting complete!');
    console.log('Final stats:', result.stats);

    return result;
  }

  /**
   * Calculate median value from an array of numbers
   * @param values Array of numbers
   * @returns Median value
   */
  private calculateMedian(values: number[]): number {
    if (values.length === 0) return 0;

    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return (sorted[mid - 1] + sorted[mid]) / 2;
    }
    return sorted[mid];
  }

  /**
   * Calculate average value from an array of numbers
   * @param values Array of numbers
   * @returns Average value
   */
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  /**
   * Helper function to fetch creator posts with retry logic
   * @param creator The creator to fetch posts for
   * @returns Object with creator and their videos
   */
  private async fetchCreatorPostsWithRetry(
    creator: TiktokVideoSchema['author'],
  ): Promise<{
    creator: TiktokVideoSchema['author'];
    videos: TiktokVideoSchema[];
  }> {
    try {
      // console.log(
      //   `Fetching posts for creator: ${creator.unique_id} (${creator.nickname})`,
      // );

      const videos = await this.executeServiceMethod(
        `Getting creator posts for: ${creator.unique_id}`,
        (service) =>
          service.getCreatorPosts(
            creator.uid,
            creator.sec_uid,
            0,
            SCOUTING_CONFIG.CREATOR_POSTS_PER_FETCH,
          ),
      );

      // console.log(
      //   `Fetched ${videos.length} posts for creator: ${creator.unique_id}`,
      // );

      return { creator, videos };
    } catch (error) {
      console.error(
        `Error fetching posts for creator ${creator.unique_id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Select challenges from keywords using either random or intelligent selection
   * @param keywords Array of keywords to search challenges for
   * @param options Configuration options for challenge selection
   * @returns Array of selected challenge IDs
   */
  async selectChallengesFromKeywords(
    keywords: string[],
    options: {
      useIntelligentSelection?: boolean;
      topN?: number;
      selectK?: number;
      targetCreatorDescription?: string;
      scoutGuidance?: string;
      agent?: Agent<any, ToolsInput, Record<string, Metric>>;
      pickerMode: 'STRATEGIC' | 'OPPORTUNITY' | 'VOLUME';
    },
  ) {
    const {
      useIntelligentSelection,
      topN = SCOUTING_CONFIG.DEFAULT_TOP_N_CHALLENGES,
      selectK = SCOUTING_CONFIG.DEFAULT_SELECT_K_CHALLENGES,
      targetCreatorDescription,
      scoutGuidance,
      agent,
      pickerMode,
    } = options;

    console.log(`Selecting challenges from keywords: ${keywords.join(', ')}`);
    console.log(
      `Selection mode: ${useIntelligentSelection ? 'Intelligent' : 'Random'}`,
    );
    console.log(`Top N: ${topN}, Select K: ${selectK}`);

    // Step 1: Search for challenges for all keywords
    const allChallenges: TiktokChallengeSchema[] = [];

    const keywordLength = keywords.length;
    const totalTopN = topN * keywordLength;
    const totalSelectK = selectK * keywordLength;

    for (const keyword of keywords) {
      try {
        const sanitizedKeyword = keyword.replace(/#/g, '').trim();
        // console.log(`Searching challenges for keyword: "${sanitizedKeyword}"`);

        const challenges = await this.searchTiktokChallenges(
          sanitizedKeyword,
          0,
          20,
        );
        console.log(
          `Found ${challenges.length} challenges for keyword "${sanitizedKeyword}"`,
        );

        allChallenges.push(...challenges);
      } catch (error) {
        console.error(
          `Error searching challenges for keyword "${keyword}":`,
          error,
        );
        // Continue with next keyword
      }
    }

    // Step 2: Deduplicate and sort by view count
    const uniqueChallenges = allChallenges
      .filter(
        (challenge, index, self) =>
          index ===
          self.findIndex((c) => c.challenge_id === challenge.challenge_id),
      )
      .sort((a, b) => b.view_count - a.view_count);

    console.log(`Found ${uniqueChallenges.length} unique challenges total`);

    // Step 3: Take top N challenges
    const topChallenges = uniqueChallenges.slice(0, totalTopN);
    console.log(
      `Selected top ${topChallenges.length} challenges by view count`,
    );

    if (topChallenges.length === 0) {
      console.warn('No challenges found for the given keywords');
      return [];
    }

    // Step 4: Select K challenges using the specified method
    let selectedChallenges: TiktokChallengeSchema[];

    if (
      useIntelligentSelection &&
      agent &&
      targetCreatorDescription &&
      scoutGuidance
    ) {
      console.log(
        'Using intelligent challenge selection, pickerMode: ',
        pickerMode,
      );
      selectedChallenges = await this.selectChallengesIntelligently(
        topChallenges,
        targetCreatorDescription,
        scoutGuidance,
        totalSelectK,
        agent,
        pickerMode,
      );
    } else {
      console.log('Using random challenge selection...');
      selectedChallenges = this.selectChallengesRandomly(
        topChallenges,
        totalSelectK,
      );
    }

    console.log(`Final selection: ${selectedChallenges.length} challenges`);
    selectedChallenges.forEach((challenge, index) => {
      console.log(
        `${index + 1}. ${challenge.challenge_name} (ID: ${challenge.challenge_id}, Views: ${challenge.view_count})`,
      );
    });

    return selectedChallenges.map((c) => c.challenge_id);
  }

  /**
   * Select challenges randomly from the top challenges
   * @param challenges Array of challenges to select from
   * @param selectK Number of challenges to select
   * @returns Array of selected challenges
   */
  private selectChallengesRandomly(
    challenges: TiktokChallengeSchema[],
    selectK: number,
  ): TiktokChallengeSchema[] {
    const shuffled = [...challenges].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, Math.min(selectK, challenges.length));
  }

  /**
   * Select challenges intelligently using the challenge picker agent with batching
   * @param challenges Array of challenges to select from
   * @param targetCreatorDescription Description of target creators
   * @param scoutGuidance Additional guidance from the scout agent
   * @param totalSelectK Total number of challenges to select
   * @param pickerAgent Challenge picker agent instance
   * @param pickerMode Mode for the challenge picker agent
   * @returns Array of selected challenges
   */
  private async selectChallengesIntelligently(
    challenges: TiktokChallengeSchema[],
    targetCreatorDescription: string,
    scoutGuidance: string,
    totalSelectK: number,
    pickerAgent: Agent<any, ToolsInput, Record<string, Metric>>,
    pickerMode: string,
  ): Promise<TiktokChallengeSchema[]> {
    try {
      if (!pickerAgent) {
        console.warn(
          'Challenge picker agent not found, falling back to random selection',
        );
        return this.selectChallengesRandomly(challenges, totalSelectK);
      }

      console.log(`🤖 Starting intelligent challenge selection with batching`);
      console.log(`📊 Total challenges to process: ${challenges.length}`);
      console.log(`🎯 Target selection count: ${totalSelectK}`);
      console.log(
        `📦 Batch size: ${SCOUTING_CONFIG.CHALLENGE_PICKER_BATCH_SIZE}`,
      );

      const batchSize = SCOUTING_CONFIG.CHALLENGE_PICKER_BATCH_SIZE;
      const allSelectedChallenges: TiktokChallengeSchema[] = [];
      const processedChallengeIds = new Set<string>();

      // Process challenges in batches
      for (let i = 0; i < challenges.length; i += batchSize) {
        // Check if we already have enough challenges
        if (allSelectedChallenges.length >= totalSelectK) {
          console.log(
            `✅ Target reached: ${allSelectedChallenges.length}/${totalSelectK} challenges selected`,
          );
          break;
        }

        const batch = challenges.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(challenges.length / batchSize);

        console.log(
          `🔄 Processing batch ${batchNumber}/${totalBatches} with ${batch.length} challenges`,
        );

        try {
          // Don't specify how many to select - let the agent decide
          const prompt = `Challenge Requirements: ${targetCreatorDescription}

Scout Guidance: ${scoutGuidance}

Mode: ${pickerMode}

Please analyze and select the most relevant challenges from the following batch. Select as many as you find suitable based on the requirements (no specific count limit):

Challenge Data: ${JSON.stringify(batch, null, 2)}`;

          const userMessage = {
            role: 'user' as const,
            content: prompt,
          };

          const resp = await pickerAgent.generate([userMessage], {
            output: ChallengPickerOutputSchema,
            temperature: 0.8,
          });

          console.log(`📋 Batch ${batchNumber} agent response:`, {
            mode: resp.object.mode,
            selected_count: resp.object.selected_challenges.length,
            avg_score: resp.object.summary.avg_score,
          });

          // Filter out already processed challenges and add new ones
          const newSelectedChallenges = resp.object.selected_challenges
            .filter((item) => !processedChallengeIds.has(item.challenge_id))
            .map((item) => {
              const challenge = batch.find(
                (c) => c.challenge_id === item.challenge_id,
              );
              if (challenge) {
                processedChallengeIds.add(item.challenge_id);
                return challenge;
              }
              return null;
            })
            .filter(Boolean) as TiktokChallengeSchema[];

          allSelectedChallenges.push(...newSelectedChallenges);

          console.log(
            `✨ Batch ${batchNumber} yielded ${newSelectedChallenges.length} new challenges`,
          );
          console.log(
            `📈 Total selected so far: ${allSelectedChallenges.length}/${totalSelectK}`,
          );
        } catch (error) {
          console.error(`❌ Error processing batch ${batchNumber}:`, error);
          console.log('⏭️  Continuing with next batch...');
        }
      }

      if (allSelectedChallenges.length > 0) {
        // Sort by view count (descending) and limit to target count
        const finalSelection = allSelectedChallenges
          .sort((a, b) => b.view_count - a.view_count)
          .slice(0, totalSelectK);

        console.log(
          `🎉 Intelligent selection complete: ${finalSelection.length} challenges selected`,
        );
        return finalSelection;
      } else {
        console.warn(
          '⚠️  No challenges selected by agent, falling back to random selection',
        );
        return this.selectChallengesRandomly(challenges, totalSelectK);
      }
    } catch (error) {
      console.error('❌ Error in intelligent challenge selection:', error);
      console.log('🔄 Falling back to random selection');
      return this.selectChallengesRandomly(challenges, totalSelectK);
    }
  }
}

// Export a singleton instance
export const videoScouter = new VideoScouterService();
